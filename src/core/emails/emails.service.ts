import { Injectable, Logger } from '@nestjs/common';
import { MailerService } from '@nestjs-modules/mailer';

@Injectable()
export class EmailsService {
  private readonly logger = new Logger(EmailsService.name);

  constructor(private readonly mailerService: MailerService) {}

  async sendWelcomeEmail(userEmail: string, userName: string): Promise<void> {
    try {
      await this.mailerService.sendMail({
        to: userEmail,
        subject: 'Welcome to Hold X!',
        template: './welcome.ejs',
        context: {
          userName,
          platformName: 'Hold EXx',
          loginUrl: 'https://holdx.com/login',
          unsubscribeUrl: 'https://holdx.com/unsubscribe',
          platformDomain: 'holdx.com',
        },
      });
    } catch (error) {
      this.logger.error(error);
    }
  }

  async sendVerificationEmail(
    userEmail: string,
    userName: string,
    verificationToken: string,
  ): Promise<void> {
    try {
      await this.mailerService.sendMail({
        to: userEmail,
        subject: 'Verify Your Email Address',
        template: './verify-account.ejs',
        context: {
          userName,
          platformName: 'Hold X',
          verificationToken,
          unsubscribeUrl: 'https://holdx.com/unsubscribe',
          platformDomain: 'holdx.com',
        },
      });
    } catch (error) {
      this.logger.error(error);
    }
  }
  async sendResetPasswordEmail(
    userEmail: string,
    userName: string,
    resetToken: string,
  ): Promise<void> {
    try {
      await this.mailerService.sendMail({
        to: userEmail,
        subject: 'Reset Your Password',
        template: './reset-password.ejs',
        context: {
          userName,
          platformName: 'Hold X',
          resetToken,
          unsubscribeUrl: 'https://holdx.com/unsubscribe',
          platformDomain: 'holdx.com',
        },
      });
    } catch (error) {
      this.logger.error(error);
    }
  }
  async sendCustomEmail(
    userEmail: string,
    userName: string,
    subject: string,
    message: string,
  ): Promise<void> {
    try {
      await this.mailerService.sendMail({
        to: userEmail,
        subject: subject,
        template: './custom.ejs',
        context: {
          userName,
          platformName: 'Hold X',
          title: subject,

          message,
          unsubscribeUrl: 'https://holdx.com/unsubscribe',
          platformDomain: 'holdx.com',
        },
      });
    } catch (error) {
      this.logger.error(error);
    }
  }
}
