import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  ParseIntPipe,
  UseInterceptors,
  UploadedFiles,
} from '@nestjs/common';
import { TransactionService } from './transaction.service';
import { CreateTransactionDto } from './dto/create.dto';
import { UpdateTransactionStatusDto } from './dto/update-status.dto';
import { TransactionFilterDto } from './dto/transaction-filter.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/common/guards/jwt.guard';
import { User } from 'src/common/decorators/user.decorator';
import { RolesGuard } from 'src/common/guards/roles.guard';
import { Roles } from 'src/common/decorators/roles.decorator';
import { FileInterceptor } from '@nestjs/platform-express';
import { DeclineTransactionDto } from './dto/decline.dto';

@ApiTags('Transactions')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('transactions')
export class TransactionController {
  constructor(private readonly transactionService: TransactionService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new transaction' })
  @ApiResponse({ status: 201, description: 'Transaction created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'User or payment method not found' })
  @UseInterceptors(FileInterceptor('attachments'))
  async createTransaction(
    @Body() createTransactionDto: CreateTransactionDto,
    @User('id') userId: number,
    @UploadedFiles() attachments: Express.Multer.File[],
  ) {
    return this.transactionService.createTransaction(
      userId,
      createTransactionDto,
      attachments,
    );
  }

  @Get()
  @UseGuards(RolesGuard)
  @Roles('admin', 'super_admin')
  @ApiOperation({ summary: 'Get all transactions (Admin only)' })
  @ApiResponse({ status: 200, description: 'Returns list of all transactions' })
  async getAllTransactions(@Query() query: TransactionFilterDto) {
    return this.transactionService.getAllTransactions(query);
  }

  @Get('my-transactions')
  @ApiOperation({ summary: 'Get current user initiated transactions' })
  @ApiResponse({ status: 200, description: 'Returns user transactions' })
  async getUserTransactions(
    @User('id') userId: number,
    @Query() query: TransactionFilterDto,
  ) {
    return this.transactionService.getUserTransactions(userId, query);
  }

  @Get('received')
  @ApiOperation({ summary: 'Get transactions received by current user' })
  @ApiResponse({ status: 200, description: 'Returns received transactions' })
  async getReceivedTransactions(
    @User('id') userId: number,
    @Query() query: TransactionFilterDto,
  ) {
    return this.transactionService.getReceivedTransactions(userId, query);
  }

  @Post('decline')
  @ApiOperation({ summary: 'Decline a transaction' })
  @ApiResponse({
    status: 200,
    description: 'Transaction declined successfully',
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async declineTransaction(
    @Body() declineTransactionDto: DeclineTransactionDto,
    @User('id') userId: number,
  ) {
    return this.transactionService.declineTransaction(
      userId,
      declineTransactionDto,
    );
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get transaction by ID' })
  @ApiResponse({ status: 200, description: 'Returns transaction details' })
  @ApiResponse({ status: 404, description: 'Transaction not found' })
  async getTransactionById(@Param('id', ParseIntPipe) id: number) {
    return this.transactionService.getTransactionById(id);
  }

  @Patch(':id/status')
  @ApiOperation({ summary: 'Update transaction status' })
  @ApiResponse({ status: 200, description: 'Transaction status updated' })
  @ApiResponse({ status: 404, description: 'Transaction not found' })
  async updateTransactionStatus(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateStatusDto: UpdateTransactionStatusDto,
    @User('id') userId: number,
  ) {
    return this.transactionService.updateTransactionStatus(
      id,
      updateStatusDto.status,
      userId,
    );
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete transaction (Initiator only)' })
  @ApiResponse({ status: 200, description: 'Transaction deleted successfully' })
  @ApiResponse({ status: 400, description: 'Cannot delete transaction' })
  @ApiResponse({ status: 404, description: 'Transaction not found' })
  async deleteTransaction(
    @Param('id', ParseIntPipe) id: number,
    @User('id') userId: number,
  ) {
    return this.transactionService.deleteTransaction(id, userId);
  }
}
