import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty } from 'class-validator';

export class UpdateTransactionStatusDto {
  @ApiProperty({
    example: 'completed',
    description: 'Transaction status',
    enum: ['completed', 'disputed', 'cancelled', 'expired', 'failed'],
  })
  @IsEnum(['completed', 'disputed', 'cancelled', 'expired', 'failed'])
  @IsNotEmpty()
  status: 'completed' | 'disputed' | 'cancelled' | 'expired' | 'failed';
}
