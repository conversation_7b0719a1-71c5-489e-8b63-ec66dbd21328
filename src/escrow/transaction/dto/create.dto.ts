import { ApiProperty } from '@nestjs/swagger';
import {
  IsDate,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';

export class CreateTransactionDto {
  @ApiProperty({
    example: 'Transaction Title',
    description: 'Transaction title',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    example: 'Transaction Description',
    description: 'Transaction description',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    example: 'Transaction Aggrement',
    description: 'Transaction aggrement',
  })
  @IsString()
  @IsNotEmpty()
  aggrement: string;

  @ApiProperty({
    example: 'Transaction Category',
    description: 'Transaction category',
  })
  @IsString()
  @IsNotEmpty()
  category: string;

  @ApiProperty({
    example: '2021-01-01T00:00:00.000Z',
    description: 'Transaction dead line',
  })
  @IsDate()
  @IsNotEmpty()
  deadLine: string;

  @ApiProperty({
    example: ['file1.pdf', 'file2.jpg'],
    description: 'Transaction attachments',
    required: false,
  })
  @IsString({ each: true })
  @IsOptional()
  attashments?: string[];

  @ApiProperty({ example: 1, description: 'Seller ID' })
  @IsNumber()
  @IsNotEmpty()
  sellerId: number;

  @ApiProperty({
    example: 1,
    description: 'Payment method ID',
  })
  @IsNumber()
  @IsNotEmpty()
  paymentMethodId: number;

  @ApiProperty({
    example: 1000,
    description: 'Transaction amount',
  })
  @IsNumber()
  @IsNotEmpty()
  amount: number;
}
