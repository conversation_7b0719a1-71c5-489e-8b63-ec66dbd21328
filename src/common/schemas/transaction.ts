import { integer } from 'drizzle-orm/pg-core';
import {
  pgTable,
  serial,
  varchar,
  timestamp,
  pgEnum,
} from 'drizzle-orm/pg-core';
import { users } from './users';
import { escrow } from './excro';
import { paymentMethods } from './payment-method';
import { relations } from 'drizzle-orm';
import { files } from './files';
import { payments } from './payment';
export const statusEnum = pgEnum('status', [
  'completed',
  'disputed',
  'cancelled',
  'expired',
  'initiated',
  'failed',
]);
export const transactions = pgTable('transactions', {
  id: serial('id').primaryKey(),
  title: varchar('title', { length: 255 }).notNull(),
  description: varchar('description', { length: 255 }),
  aggrement: varchar('aggrement', { length: 255 }),
  category: varchar('type', { length: 255 }),
  deadLine: timestamp('dead_line'),
  attashment: varchar('attashment', { length: 255 }),
  initiatedBy: integer('user_id').references(() => users.id),
  receivedBy: integer('received_by').references(() => users.id),
  paymentMethodId: integer('payment_method_id').references(
    () => paymentMethods.id,
  ),
  amount: integer('amount').notNull(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
  status: statusEnum('status').notNull(),
});

export type Transaction = typeof transactions.$inferSelect;
export type NewTransaction = typeof transactions.$inferInsert;

export const transactionRelations = relations(
  transactions,
  ({ one, many }) => ({
    user: one(users, {
      fields: [transactions.initiatedBy],
      references: [users.id],
    }),
    escrow: one(escrow),
    receivedBy: one(users, {
      fields: [transactions.receivedBy],
      references: [users.id],
    }),
    attashement: many(files),
    payments: many(payments),
    paymentMethod: one(paymentMethods, {
      fields: [transactions.paymentMethodId],
      references: [paymentMethods.id],
    }),
  }),
);
