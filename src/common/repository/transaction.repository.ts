import { Injectable, Inject } from '@nestjs/common';
import { eq, desc, count, and } from 'drizzle-orm';
import { transactions, Transaction, NewTransaction } from '../schemas';
import * as schema from '../schemas/transaction';
import { ITransactionRepository } from '../interfaces';
import { DATABASE_CONNECTION } from 'src/configs';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { PaginatedResponse, PaginationQuery } from '../interfaces';

@Injectable()
export class TransactionRepository implements ITransactionRepository {
  constructor(
    @Inject(DATABASE_CONNECTION)
    private readonly db: NodePgDatabase<typeof schema>,
  ) {}

  async findAll(query: PaginationQuery): Promise<PaginatedResponse<any>> {
    const page = query.page || 1;
    const limit = Math.min(query.limit || 10, 100);
    const offset = (page - 1) * limit;

    const [{ count: totalItems }] = await this.db
      .select({ count: count() })
      .from(transactions);

    const data = await this.db
      .select()
      .from(transactions)
      .limit(limit)
      .offset(offset)
      .orderBy(desc(transactions.createdAt));

    const totalPages = Math.ceil(totalItems / limit);

    return {
      data,
      meta: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  async create(transactionData: NewTransaction): Promise<Transaction> {
    const result = await this.db
      .insert(transactions)
      .values({
        ...transactionData,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    return result[0];
  }

  async update(
    id: number,
    transactionData: Partial<Transaction>,
  ): Promise<Transaction | null> {
    const result = await this.db
      .update(transactions)
      .set({
        ...transactionData,
        updatedAt: new Date(),
      })
      .where(eq(transactions.id, id))
      .returning();

    return result[0] || null;
  }

  async delete(id: number): Promise<boolean> {
    const result = await this.db
      .delete(transactions)
      .where(eq(transactions.id, id))
      .returning();

    return result.length > 0;
  }

  async findById(id: number): Promise<Transaction | null> {
    const result = await this.db
      .select()
      .from(transactions)
      .where(eq(transactions.id, id))
      .limit(1);

    return result[0] || null;
  }

  async findByUserId(
    userId: number,
    query: PaginationQuery,
  ): Promise<PaginatedResponse<Transaction> | null> {
    const page = query.page || 1;
    const limit = Math.min(query.limit || 10, 100);
    const offset = (page - 1) * limit;

    const [{ count: totalItems }] = await this.db
      .select({ count: count() })
      .from(transactions);

    const result = await this.db
      .select()
      .from(transactions)
      .where(eq(transactions.initiatedBy, userId))
      .limit(limit)
      .offset(offset)
      .orderBy(desc(transactions.createdAt));

    const totalPages = Math.ceil(totalItems / limit);

    return {
      data: result,
      meta: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  async findByReceivedBy(
    userId: number,
    query: PaginationQuery,
  ): Promise<PaginatedResponse<Transaction> | null> {
    const page = query.page || 1;
    const limit = Math.min(query.limit || 10, 100);
    const offset = (page - 1) * limit;
    const [{ count: totalItems }] = await this.db
      .select({ count: count() })
      .from(transactions);

    const result = await this.db
      .select()
      .from(transactions)
      .where(eq(transactions.receivedBy, userId))
      .limit(limit)
      .offset(offset)
      .orderBy(desc(transactions.createdAt));

    const totalPages = Math.ceil(totalItems / limit);

    return {
      data: result,
      meta: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  async findByEscrowId(escrowId: number): Promise<Transaction | null> {
    const result = await this.db
      .select()
      .from(transactions)
      .where(eq(transactions.id, escrowId))
      .limit(1);

    return result[0] || null;
  }

  async findByPaymentMethodId(
    paymentMethodId: number,
    userId: number,
    query: PaginationQuery,
  ): Promise<PaginatedResponse<Transaction> | null> {
    const page = query.page || 1;
    const limit = Math.min(query.limit || 10, 100);
    const offset = (page - 1) * limit;
    const [{ count: totalItems }] = await this.db
      .select({ count: count() })
      .from(transactions);

    const result = await this.db
      .select()
      .from(transactions)
      .where(
        and(
          eq(transactions.paymentMethodId, paymentMethodId),
          eq(transactions.initiatedBy, userId),
        ),
      )
      .limit(limit)
      .offset(offset)
      .orderBy(desc(transactions.createdAt));

    const totalPages = Math.ceil(totalItems / limit);

    return {
      data: result,
      meta: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }
}
