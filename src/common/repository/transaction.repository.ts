import { Injectable, Inject } from '@nestjs/common';
import { eq, desc, count, and, like } from 'drizzle-orm';
import {
  transactions,
  Transaction,
  NewTransaction,
  escrow,
  files,
} from '../schemas';
import * as schema from '../schemas/transaction';
import { ITransactionRepository } from '../interfaces';
import { DATABASE_CONNECTION } from 'src/configs';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { PaginatedResponse, TransactionFilter } from '../interfaces';

@Injectable()
export class TransactionRepository implements ITransactionRepository {
  constructor(
    @Inject(DATABASE_CONNECTION)
    private readonly db: NodePgDatabase<typeof schema>,
  ) {}

  async findAll(query: TransactionFilter): Promise<PaginatedResponse<any>> {
    const page = query.page || 1;
    const limit = Math.min(query.limit || 10, 100);
    const offset = (page - 1) * limit;
    const status = query.status;
    const search = query.search;

    const [{ count: totalItems }] = await this.db
      .select({ count: count() })
      .from(transactions);

    const data = await this.db
      .select({
        transaction: transactions,
        escrow: escrow,
        files: files,
      })
      .from(transactions)
      .leftJoin(escrow, eq(transactions.id, escrow.transactionId))
      .leftJoin(files, eq(transactions.id, files.transactionId))
      .limit(limit)
      .offset(offset)
      .orderBy(desc(transactions.createdAt));

    const totalPages = Math.ceil(totalItems / limit);

    return {
      data,
      meta: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  async create(transactionData: NewTransaction): Promise<any> {
    const result = await this.db
      .insert(transactions)
      .values({
        ...transactionData,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    return result[0];
  }

  async update(
    id: number,
    transactionData: Partial<Transaction>,
  ): Promise<Transaction | null> {
    const result = await this.db
      .update(transactions)
      .set({
        ...transactionData,
        updatedAt: new Date(),
      })
      .where(eq(transactions.id, id))
      .returning();

    return result[0] || null;
  }

  async delete(id: number): Promise<boolean> {
    const result = await this.db
      .delete(transactions)
      .where(eq(transactions.id, id))
      .returning();

    return result.length > 0;
  }

  async findById(id: number): Promise<Transaction | null> {
    const result = await this.db
      .select()
      .from(transactions)
      .where(eq(transactions.id, id))
      .limit(1);

    return result[0] || null;
  }

  async findByUserId(
    userId: number,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<any> | null> {
    const page = query.page || 1;
    const limit = Math.min(query.limit || 10, 100);
    const offset = (page - 1) * limit;

    const [{ count: totalItems }] = await this.db
      .select({ count: count() })
      .from(transactions);

    const result = await this.db
      .select({
        transaction: transactions,
        escrow: escrow,
        files: files,
      })
      .from(transactions)
      .where(eq(transactions.initiatedBy, userId))
      .leftJoin(escrow, eq(transactions.id, escrow.transactionId))
      .leftJoin(files, eq(transactions.id, files.transactionId))
      .limit(limit)
      .offset(offset)
      .orderBy(desc(transactions.createdAt));

    const totalPages = Math.ceil(totalItems / limit);

    return {
      data: result,
      meta: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  async findByReceivedBy(
    userId: number,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<any> | null> {
    const page = query.page || 1;
    const limit = Math.min(query.limit || 10, 100);
    const offset = (page - 1) * limit;
    const [{ count: totalItems }] = await this.db
      .select({ count: count() })
      .from(transactions);

    const result = await this.db
      .select({
        transaction: transactions,
        escrow: escrow,
        files: files,
      })
      .from(transactions)
      .where(eq(transactions.receivedBy, userId))
      .leftJoin(escrow, eq(transactions.id, escrow.transactionId))
      .leftJoin(files, eq(transactions.id, files.transactionId))
      .limit(limit)
      .offset(offset)
      .orderBy(desc(transactions.createdAt));

    const totalPages = Math.ceil(totalItems / limit);

    return {
      data: result,
      meta: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  async findByEscrowId(escrowId: number): Promise<Transaction | null> {
    const result = await this.db
      .select()
      .from(transactions)
      .where(eq(transactions.id, escrowId))
      .limit(1);

    return result[0] || null;
  }

  async findByPaymentMethodId(
    paymentMethodId: number,
    userId: number,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<any> | null> {
    const page = query.page || 1;
    const limit = Math.min(query.limit || 10, 100);
    const offset = (page - 1) * limit;
    const [{ count: totalItems }] = await this.db
      .select({ count: count() })
      .from(transactions);

    const result = await this.db
      .select()
      .from(transactions)
      .where(
        and(
          eq(transactions.paymentMethodId, paymentMethodId),
          eq(transactions.initiatedBy, userId),
        ),
      )
      .leftJoin(escrow, eq(transactions.id, escrow.transactionId))
      .leftJoin(files, eq(transactions.id, files.transactionId))
      .limit(limit)
      .offset(offset)
      .orderBy(desc(transactions.createdAt));

    const totalPages = Math.ceil(totalItems / limit);

    return {
      data: result,
      meta: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }
}
