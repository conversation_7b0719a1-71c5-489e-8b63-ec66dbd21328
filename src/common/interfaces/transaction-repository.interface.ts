import { Transaction } from '../schemas';
import { PaginatedResponse, PaginationQuery } from './paginaton';
import { IRepository } from './repository.interface';

export interface ITransactionRepository extends IRepository<Transaction> {
  findByUserId(
    userId: number,
    query: PaginationQuery,
  ): Promise<PaginatedResponse<Transaction> | null>;
  findByReceivedBy(
    userId: number,
    query: PaginationQuery,
  ): Promise<PaginatedResponse<Transaction> | null>;
  findByEscrowId(escrowId: number): Promise<Transaction | null>;
  findByPaymentMethodId(
    paymentMethodId: number,
    userId: number,
    query: PaginationQuery,
  ): Promise<PaginatedResponse<Transaction> | null>;
}
